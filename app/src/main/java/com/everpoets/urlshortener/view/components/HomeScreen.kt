package com.everpoets.urlshortener.view.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.everpoets.urlshortener.ShortenUiState
import com.everpoets.urlshortener.ui.theme.SPACING_10
import com.everpoets.urlshortener.ui.theme.SPACING_16
import com.everpoets.urlshortener.view.HomeAction

@Composable
fun HomeScreen(
    snackbarHostState: SnackbarHostState,
    uiState: ShortenUiState,
    dispatchAction: (HomeAction) -> Unit,
) {
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        snackbarHost = {
            SnackbarHost(hostState = snackbarHostState)
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .padding(innerPadding)
                .padding(horizontal = SPACING_16),
            verticalArrangement = Arrangement.spacedBy(SPACING_10)
        ) {
            InputTextButton(
                onClick = { dispatchAction(HomeAction.ShortenUrl(it)) }
            )
            ShortenUrlList(
                uiState = uiState
            )
        }
    }
}