package com.everpoets.urlshortener

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.res.stringResource
import com.everpoets.urlshortener.ui.theme.UrlShortenerTheme
import com.everpoets.urlshortener.view.HomeAction
import com.everpoets.urlshortener.view.components.HomeScreen
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HomeActivity : ComponentActivity() {
    private val mainViewModel: MainViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            UrlShortenerTheme {
                val snackbarHostState = remember { SnackbarHostState() }
                val uiState by mainViewModel.shortenUiState.collectAsState()
                val userMessage = uiState.userMessage?.let { stringResource(it) }

                if (userMessage != null) {
                    LaunchedEffect(userMessage) {
                        snackbarHostState.showSnackbar(userMessage)
                        mainViewModel.dispatchAction(HomeAction.ClearUserMessage)
                    }
                }

                HomeScreen(
                    snackbarHostState = snackbarHostState,
                    uiState = uiState,
                    dispatchAction = {
                        mainViewModel.dispatchAction(it)
                    }
                )
            }
        }
    }
}
